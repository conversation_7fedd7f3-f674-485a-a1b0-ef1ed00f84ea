# This workflow only test image can be built.
name: Continue Integration

on:
  pull_request:
    branches: [ "master", "dev" ]
    paths-ignore:
      - 'docs/**'
      - '*.md'
      - 'model_deployment/**'
      - 'docker-compose.yml'
      - 'renovate.json'

env:
  PG_USERNAME: postgres
  PG_PASSWORD: testpass
  PG_HOST: postgres
  PG_PORT: 5432
  PG_DATABASE: line-art-generator
  DOCKER_NETWORK: ci-network
  APP_NAME: coloraria

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Create Docker network
      run: docker network create ${{ env.DOCKER_NETWORK }}

    - name: Run PostgreSQL container
      run: |
        docker run -d \
          --name ${{ env.PG_HOST }} \
          --network ${{ env.DOCKER_NETWORK }} \
          -e POSTGRES_USER=${{ env.PG_USERNAME }} \
          -e POSTGRES_PASSWORD=${{ env.PG_PASSWORD }} \
          -e POSTGRES_DB=${{ env.PG_DATABASE }} \
          postgres

    - name: Create Docker buildx builder
      run: |
        docker buildx create --name image-builder --driver docker-container --use --driver-opt network=${{ env.DOCKER_NETWORK }}

    - name: Bootstrap buildx builder
      run: docker buildx inspect --bootstrap

    - name: Build Docker image with buildx
      run: |
        POSTGRES_IP=$(docker inspect -f '{{(index .NetworkSettings.Networks "${{ env.DOCKER_NETWORK }}").IPAddress}}' ${{ env.PG_HOST }})
        docker buildx build \
          --add-host=postgres:$POSTGRES_IP \
          --build-arg PG_USERNAME=${{ env.PG_USERNAME }} \
          --build-arg PG_PASSWORD=${{ env.PG_PASSWORD }} \
          --build-arg PG_HOST=${{ env.PG_HOST }} \
          --build-arg PG_PORT=${{ env.PG_PORT }} \
          --build-arg PG_DATABASE=${{ env.PG_DATABASE }} \
          -t ${{ secrets.DOCKER_USERNAME }}/${{ env.APP_NAME }}:latest .
