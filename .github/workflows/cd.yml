# This workflow will build image and push to image registry
name: Continuous Deployment

permissions:
  contents: write

on:
  push:
    branches: [ "master" ]
    paths-ignore:
      - 'docs/**'
      - '*.md'
      - 'model_deployment/**'
      - 'docker-compose.yml'
      - 'renovate.json'

env:
  DOCKER_NETWORK: cd-network
  APP_NAME: coloraria
  HETZNER_FIREWALL_RULE: cd-pipeline

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        # This is the crucial line that fetches all history and tags
        fetch-depth: 0

    - name: Format Branch Name
      run: |
        BRANCH_CLEAN=$(echo "${{ github.ref_name }}" | tr '/' '-')
        echo "CLEAN_BRANCH=$BRANCH_CLEAN" >> $GITHUB_ENV

    - name: Fetch Tags
      run: git fetch --tags --force
    
    - name: Generate Semantic Version
      id: version
      uses: paulhatch/semantic-version@v5.4.0
      with:
        # The prefix to use to identify tags
        tag_prefix: "v"
        # A string which, if present in a git commit, indicates a breaking change
        major_pattern: "^(major):"
        # A string which, if present in a git commit, indicates a new feature
        minor_pattern: "^(feat|feature):"
        # If true, the body of commits will also be searched for major/minor patterns
        search_commit_body: true
        # The version format
        version_format: "v${major}.${minor}.${patch}"
        bump_each_commit: false
    
    - name: 'Get GitHub Runner IP'
      id: runner_ip
      run: echo "ip=$(curl -s ifconfig.me)" >> $GITHUB_OUTPUT

    - name: 'Get Hetzner Firewall ID from Name'
      id: get_firewall_id
      run: |
        API_RESPONSE=$(curl -fsS -s -H "Authorization: Bearer ${{ secrets.HETZNER_API_TOKEN }}" "https://api.hetzner.cloud/v1/firewalls?name=${{ env.HETZNER_FIREWALL_RULE }}")
        FIREWALL_ID=$(echo $API_RESPONSE | jq '.firewalls[0].id')
        if [ -z "$FIREWALL_ID" ] || [ "$FIREWALL_ID" == "null" ]; then
          echo "::error::Could not find Firewall with name '${{ env.HETZNER_FIREWALL_RULE }}'"
          exit 1
        fi
        echo "id=${FIREWALL_ID}" >> $GITHUB_OUTPUT

    - name: 'Allow Runner IP in Hetzner Firewall'
      id: set_firewall_rule
      run: |
        curl -fsS -X POST \
        -H "Authorization: Bearer ${{ secrets.HETZNER_API_TOKEN }}" \
        -H "Content-Type: application/json" \
        -d '{
              "rules": [
                {
                  "direction": "in",
                  "protocol": "tcp",
                  "port": "${{ secrets.PROD_DB_PORT }}",
                  "source_ips": ["${{ steps.runner_ip.outputs.ip }}/32"],
                  "description": "DB Access"
                },
                {
                  "direction": "in",
                  "protocol": "tcp",
                  "port": "22",
                  "source_ips": ["${{ steps.runner_ip.outputs.ip }}/32"],
                  "description": "Allow SSH"
                }
              ]
            }' \
        "https://api.hetzner.cloud/v1/firewalls/${{ steps.get_firewall_id.outputs.id }}/actions/set_rules"

    - name: Log in to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_TOKEN }}

    - name: Build Docker image and Apply DB migrations
      run: |
        docker build \
          --build-arg PG_USERNAME=${{ secrets.PROD_DB_USERNAME }} \
          --build-arg PG_PASSWORD=${{ secrets.PROD_DB_PASSWORD }} \
          --build-arg PG_HOST=${{ secrets.SERVER_IPV4 }} \
          --build-arg PG_PORT=${{ secrets.PROD_DB_PORT }} \
          --build-arg PG_DATABASE=${{ secrets.PROD_DB_NAME }} \
          -t ${{ secrets.DOCKER_USERNAME }}/${{ env.APP_NAME }}:${{ steps.version.outputs.version }} \
          -t ${{ secrets.DOCKER_USERNAME }}/${{ env.APP_NAME }}:latest . --push

    - uses: webfactory/ssh-agent@v0.4.1
      with:
        ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}
    - run: mkdir -p ~/.ssh/ && ssh-keyscan -H ${{ secrets.SERVER_IPV4 }} >> ~/.ssh/known_hosts
    - run: |
        ssh ${{ vars.WORKER }}@${{ secrets.SERVER_IPV4 }} \
        "cd line-art-generator-v2 && \
        git checkout master && \
        git pull origin master && \
        export DOCKER_USERNAME=${{ secrets.DOCKER_USERNAME }} && \
        export DOCKER_TOKEN=${{ secrets.DOCKER_TOKEN }} && \
        export APP_NAME=${{ env.APP_NAME }} && \
        scripts/run.sh"

    - name: 'Revert Firewall Rule'
      if: always()
      run: |
        echo "Reverting firewall to an empty rule set"
        curl -fsS -X POST \
        -H "Authorization: Bearer ${{ secrets.HETZNER_API_TOKEN }}" \
        -H "Content-Type: application/json" \
        -d '{
              "rules": []
            }' \
        "https://api.hetzner.cloud/v1/firewalls/${{ steps.get_firewall_id.outputs.id }}/actions/set_rules"

    - name: Push Release Tag
      if: contains(fromJson('["master"]'), github.ref_name)
      run: |
        git tag ${{ steps.version.outputs.version }}
        git push origin ${{ steps.version.outputs.version }}
