"""
Pydantic models for API requests and responses.
"""

from typing import List, Optional
from pydantic import BaseModel
from .config import DEFAULT_WIDTH, DEFAULT_HEIGHT, DEFAULT_BATCH_SIZE


class Text2ImageRequestBody(BaseModel):
    """Request model for text-to-image generation."""
    prompt: str
    image_urls: List[str]
    batch_size: int = DEFAULT_BATCH_SIZE
    width: int = DEFAULT_WIDTH
    height: int = DEFAULT_HEIGHT


class ImageMetadata(BaseModel):
    """Metadata for a generated image."""
    image_id: str
    width: int
    height: int
    aspect_ratio: float
    size: int
    error: Optional[str] = None


class ImagesResponse(BaseModel):
    """Response containing list of generated images."""
    images: List[ImageMetadata]
