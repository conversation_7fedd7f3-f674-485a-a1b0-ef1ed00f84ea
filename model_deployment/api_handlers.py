"""
API handlers module for Modal ComfyUI deployment.
Contains common methods and processes for API endpoints.
"""

import shutil
from typing import Optional, Dict, Any
from pathlib import Path
from fastapi import UploadFile
from PIL import Image

from .config import (
    COMFYUI_INPUT_DIR, COMFYUI_OUTPUT_DIR
)
from .utils import (
    find_node_by_type,
    generate_random_seed,
    save_workflow_json,
    find_output_files, 
    create_image_filename
)
from .image_upload import upload_file_to_r2


# Import models from models module
from .models import ImageMetadata


def save_uploaded_image(image: UploadFile) -> tuple[str, Path]:
    """
    Save an uploaded image to the ComfyUI input directory.
    
    Args:
        image: The uploaded image file
        
    Returns:
        Tuple of (filename, full_path)
    """
    comfy_input_dir = Path(COMFYUI_INPUT_DIR)
    original_filename = image.filename or "uploaded_image"
    image_filename = create_image_filename(original_filename)
    destination_path = comfy_input_dir / image_filename
    
    try:
        with open(destination_path, "wb") as buffer:
            shutil.copyfileobj(image.file, buffer)
    finally:
        image.file.close()
    
    return image_filename, destination_path


def get_image_dimensions(image_path: Path) -> tuple[int, int]:
    """
    Get dimensions of an image file.
    
    Args:
        image_path: Path to the image file
        
    Returns:
        Tuple of (width, height)
    """
    with Image.open(image_path) as img:
        return img.size


def configure_workflow_common(workflow_data: Dict[str, Any], image_id: str, 
                            save_node_type: str = "SaveImage") -> Dict[str, Any]:
    """
    Apply common workflow configurations.
    
    Args:
        workflow_data: The workflow JSON data
        image_id: Unique identifier for the output image
        save_node_type: Type of save node to configure
        
    Returns:
        Modified workflow data
    """
    # Find and configure the save image node
    save_image_node = find_node_by_type(workflow_data, save_node_type)
    if save_image_node:
        save_image_node["inputs"]["filename_prefix"] = image_id
    
    return workflow_data


def configure_t2i_workflow(workflow_data: Dict[str, Any], prompt: str, width: int, 
                          height: int, image_id: str) -> Dict[str, Any]:
    """
    Configure workflow for text-to-image generation.
    
    Args:
        workflow_data: The workflow JSON data
        prompt: Text prompt
        width: Image width
        height: Image height
        image_id: Unique identifier for the output image
        
    Returns:
        Configured workflow data
    """
    # Find and configure nodes
    empty_latent_node = find_node_by_type(workflow_data, "EmptyLatentImage")
    text_encoder_node = find_node_by_type(workflow_data, "CLIPTextEncodeFlux")
    k_sampler_node = find_node_by_type(workflow_data, "KSampler")
    
    # Configure text encoder
    if text_encoder_node:
        text_encoder_node["inputs"]["t5xxl"] += prompt
    
    # Configure image dimensions
    if empty_latent_node:
        empty_latent_node["inputs"]["width"] = width
        empty_latent_node["inputs"]["height"] = height
    
    # Set random seed
    if k_sampler_node:
        k_sampler_node["inputs"]["seed"] = generate_random_seed()

        # # increase steps for high resolution and non-square image
        # if (width >= 1024 and height >= 1024) and width != height:
        #     k_sampler_node["inputs"]["steps"] = 50
    
    # Apply common configurations
    return configure_workflow_common(workflow_data, image_id)


def configure_i2i_workflow(workflow_data: Dict[str, Any], image_filename: str, 
                          image_id: str) -> Dict[str, Any]:
    """
    Configure workflow for image-to-image generation.
    
    Args:
        workflow_data: The workflow JSON data
        image_filename: Name of the input image file
        image_id: Unique identifier for the output image
        
    Returns:
        Configured workflow data
    """
    # Find and configure nodes
    load_image_node = find_node_by_type(workflow_data, "LoadImage")
    k_sampler_node = find_node_by_type(workflow_data, "KSamplerAdvanced")
    
    # Configure input image
    if load_image_node:
        load_image_node["inputs"]["image"] = image_filename
    
    # Set random seed
    if k_sampler_node:
        k_sampler_node["inputs"]["noise_seed"] = generate_random_seed()
    
    # Apply common configurations
    return configure_workflow_common(workflow_data, image_id)


def configure_rmbg_workflow(workflow_data: Dict[str, Any], image_filename: str, 
                           image_id: str) -> Dict[str, Any]:
    """
    Configure workflow for background removal.
    
    Args:
        workflow_data: The workflow JSON data
        image_filename: Name of the input image file
        image_id: Unique identifier for the output image
        
    Returns:
        Configured workflow data
    """
    # Find and configure nodes
    load_image_node = find_node_by_type(workflow_data, "LoadImage")
    
    # Configure input image
    if load_image_node:
        load_image_node["inputs"]["image"] = image_filename
    
    # Apply common configurations
    return configure_workflow_common(workflow_data, image_id)


def create_temp_workflow(workflow_data: Dict[str, Any], image_id: str) -> Path:
    """
    Create a temporary workflow file for execution.
    
    Args:
        workflow_data: The configured workflow data
        image_id: Unique identifier for the workflow
        
    Returns:
        Path to the temporary workflow file
    """
    temp_workflow_path = Path(f"{image_id}.json")
    save_workflow_json(workflow_data, temp_workflow_path)
    return temp_workflow_path


def upload_output_image(image_id: str, upload_url: str) -> Optional[ImageMetadata]:
    """
    Upload generated image to the provided URL and return metadata.
    Automatically calculates actual image dimensions and aspect ratio from the generated file.

    Args:
        image_id: Unique identifier for the image
        upload_url: URL to upload the image to

    Returns:
        ImageMetadata if successful, None otherwise
    """
    output_dir = Path(COMFYUI_OUTPUT_DIR)
    output_files = find_output_files(output_dir, image_id)

    if not output_files:
        print(f"No output files found for image_id: {image_id}")
        return None

    # Use the first matching file
    output_file = output_files[0]

    # Calculate actual image dimensions from the generated file
    try:
        actual_width, actual_height = get_image_dimensions(output_file)
    except Exception as e:
        print(f"Failed to get image dimensions for {output_file}: {e}")
        return None

    metadata = upload_file_to_r2(
        upload_url=upload_url,
        file_path=output_file,
        image_id=image_id,
        width=actual_width,
        height=actual_height
    )
    return metadata
