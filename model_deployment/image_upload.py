"""
Image upload functions for R2 storage.
Handles uploading images with optimization and proper error handling.
"""

import io
import requests
from typing import Union, Optional
from pathlib import Path
from PIL import Image

from .models import ImageMetadata
from .image_optimization import auto_optimize_image


class UploadError(Exception):
    """Exception raised when image upload fails."""
    pass


def upload_image_to_r2(
    upload_url: str,
    image_data: bytes,
    image_id: str,
    width: int,
    height: int,
    timeout: int = 30,
    optimize: bool = True,
    max_size: Optional[tuple[int, int]] = None
) -> ImageMetadata:
    """
    Upload image data to R2 storage using a presigned URL.

    Args:
        upload_url: The presigned URL for uploading to R2
        image_data: The binary image data to upload
        image_id: Unique identifier for the image
        width: Image width in pixels
        height: Image height in pixels
        timeout: Request timeout in seconds (default: 30)
        optimize: Whether to optimize the image before upload (default: True)
        max_size: Optional tuple (width, height) for maximum size during optimization

    Returns:
        ImageMetadata object if upload successful

    Raises:
        UploadError: If upload fails for any reason
    """
    try:
        # Optimize image if requested
        if optimize:
            original_size = len(image_data)
            image_data = auto_optimize_image(image_data, max_size=max_size, png_aggressive=True)
            optimized_size = len(image_data)
            if optimized_size < original_size:
                print(f"Image {image_id} optimized: {original_size} -> {optimized_size} bytes ({((original_size - optimized_size) / original_size * 100):.1f}% reduction)")

        # Detect content type based on image format
        try:
            img = Image.open(io.BytesIO(image_data))
            if img.format == 'PNG':
                content_type = 'image/png'
            elif img.format in ('JPEG', 'JPG'):
                content_type = 'image/jpeg'
            elif img.format == 'GIF':
                content_type = 'image/gif'
            elif img.format == 'WEBP':
                content_type = 'image/webp'
            else:
                content_type = 'application/octet-stream'
            print(f"Uploading {image_id} as {content_type}")
        except Exception:
            # Fallback to generic type if detection fails
            content_type = 'application/octet-stream'
            print(f"Uploading {image_id} as {content_type} (format detection failed)")

        response = requests.put(
            upload_url,
            data=image_data,
            timeout=timeout,
            headers={'Content-Type': content_type}
        )

        if response.status_code == 200:
            return ImageMetadata(
                image_id=image_id,
                width=width,
                height=height,
                aspect_ratio=width / height if height > 0 else 1.0,
                size=len(image_data)
            )
        else:
            error_msg = f"HTTP {response.status_code}: Upload failed"
            print(f"Failed to upload image {image_id}: {error_msg}")
            if response.text:
                print(f"Response body: {response.text}")
            return ImageMetadata(
                image_id=image_id,
                width=width,
                height=height,
                aspect_ratio=width / height if height > 0 else 1.0,
                size=len(image_data),
                error=error_msg
            )

    except requests.exceptions.Timeout:
        error_msg = f"Upload timeout after {timeout} seconds"
        print(f"Timeout uploading image {image_id} after {timeout} seconds")
        return ImageMetadata(
            image_id=image_id,
            width=width,
            height=height,
            aspect_ratio=width / height if height > 0 else 1.0,
            size=len(image_data),
            error=error_msg
        )

    except requests.exceptions.ConnectionError as e:
        error_msg = "Connection error during upload"
        print(f"Connection error uploading image {image_id}: {e}")
        return ImageMetadata(
            image_id=image_id,
            width=width,
            height=height,
            aspect_ratio=width / height if height > 0 else 1.0,
            size=len(image_data),
            error=error_msg
        )

    except requests.exceptions.RequestException as e:
        error_msg = f"Request error: {str(e)}"
        print(f"Request error uploading image {image_id}: {e}")
        return ImageMetadata(
            image_id=image_id,
            width=width,
            height=height,
            aspect_ratio=width / height if height > 0 else 1.0,
            size=len(image_data),
            error=error_msg
        )

    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        print(f"Unexpected error uploading image {image_id}: {e}")
        return ImageMetadata(
            image_id=image_id,
            width=width,
            height=height,
            aspect_ratio=width / height if height > 0 else 1.0,
            size=len(image_data),
            error=error_msg
        )


def upload_file_to_r2(
    upload_url: str,
    file_path: Union[str, Path],
    image_id: str,
    width: int,
    height: int,
    timeout: int = 30,
    optimize: bool = True,
    max_size: Optional[tuple[int, int]] = None
) -> ImageMetadata:
    """
    Upload a file to R2 storage using a presigned URL.

    Args:
        upload_url: The presigned URL for uploading to R2
        file_path: Path to the file to upload
        image_id: Unique identifier for the image
        width: Image width in pixels
        height: Image height in pixels
        timeout: Request timeout in seconds (default: 30)
        optimize: Whether to optimize the image before upload (default: True)
        max_size: Optional tuple (width, height) for maximum size during optimization

    Returns:
        ImageMetadata object if upload successful

    Raises:
        UploadError: If upload fails for any reason
    """
    try:
        file_path = Path(file_path)
        if not file_path.exists():
            raise UploadError(f"File not found: {file_path}")

        with open(file_path, 'rb') as f:
            image_data = f.read()

        return upload_image_to_r2(
            upload_url=upload_url,
            image_data=image_data,
            image_id=image_id,
            width=width,
            height=height,
            timeout=timeout,
            optimize=optimize,
            max_size=max_size
        )
    except Exception as e:
        return ImageMetadata(
            image_id=image_id,
            width=width,
            height=height,
            aspect_ratio=width / height if height > 0 else 1.0,
            size=0,
            error=str(e)
        )
