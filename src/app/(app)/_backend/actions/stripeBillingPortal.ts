'use server'
import { cookies } from 'next/headers'
import { getPayload } from 'payload'
import { stripe } from '@/lib/stripe'
import config from '@payload-config'
import UserDAO from '../common/dao/UserDAO'
import { UnauthorizedUser } from '../common/exception'
import UserService from '../common/service/UserService'
import { _verifyJWT } from '../common/utils/auth'

export async function createStripeBillingPortalSession() {
  try {
    const token = (await cookies()).get('la-token')?.value
    const verifyPayloadToken = await _verifyJWT(token || '')
    if (!verifyPayloadToken) {
      throw new UnauthorizedUser('User is not authorized')
    }
    //Get user's customer ID from db
    const userId = verifyPayloadToken.id
    const payload = await getPayload({ config })
    const userDAO = new UserDAO(payload)
    const userService = new UserService(userDAO)

    const user = await userService.getUserWithSubscriptionPlan(userId)
    if (!user) {
      throw new UnauthorizedUser('User not found')
    }
    const userSubscriptionRecord = user.subscriptionRecord

    if (userSubscriptionRecord.stripeCustomerId == null) {
      throw new UnauthorizedUser('No active subscription found for user')
    }

    const portalSession = await stripe.billingPortal.sessions.create({
      customer: userSubscriptionRecord.stripeCustomerId,
      return_url: `${process.env.NEXT_PUBLIC_HOST}/dashboard/settings`,
    })

    return { id: portalSession.id, url: portalSession.url }
  } catch (error) {
    console.error('Error creating billing portal session:', error)
    throw new Error('Failed to create billing portal session')
  }
}
