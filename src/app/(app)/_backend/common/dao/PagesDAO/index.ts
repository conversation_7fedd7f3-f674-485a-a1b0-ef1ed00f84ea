import { BasePayload } from 'payload'

class PagesDAO {
  private payload: BasePayload

  constructor(payload: BasePayload) {
    this.payload = payload
  }

  async getPageBySlug(slug: string, onlyPublished = true) {
    const where: Record<string, any> = {
      and: [
        {
          slug: {
            equals: slug,
          },
        },
      ],
    }

    if (onlyPublished) {
      where.and.push({
        publishDate: {
          less_than_equal: new Date().toISOString(),
        },
        _status: {
          equals: 'published',
        },
      })
    }

    const post = await this.payload.find({
      collection: 'pages',
      where,
    })

    return post
  }
}

export default PagesDAO
