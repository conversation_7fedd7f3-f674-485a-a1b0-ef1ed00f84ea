import NextImage from 'next/image'
import React from 'react'
import { MoonLoader } from 'react-spinners'
import { toast } from 'sonner'
import { createStripeBillingPortalSession } from '@/app/(app)/_backend/actions/stripeBillingPortal'
import { Button } from '..'
import Text from '../../Text'

function BillingPortalButton() {
  const [isLoading, setIsLoading] = React.useState(false)

  async function handlePortalSession() {
    setIsLoading(true)
    try {
      const result = await createStripeBillingPortalSession()
      window.location.href = result.url
    } catch (error) {
      console.error('Error creating billing portal session:', error)
      toast.error('Failed to redirect to billing portal. Please try again later.', {
        position: 'top-right',
      })
    } finally {
      setIsLoading(false)
    }
  }
  return (
    <Button
      variant="secondary"
      disabled={isLoading}
      className="w-full"
      onClick={() => handlePortalSession()}
    >
      {isLoading ? (
        <MoonLoader size={20} color="#ffffff" className="mr-2" />
      ) : (
        <>
          <NextImage
            className="w-6 h-6"
            src="/media/icons/stripe.svg"
            width={24}
            height={24}
            loading="lazy"
            alt="stripe logo"
          />
          <Text>Manage my Stripe Subscription</Text>
        </>
      )}
    </Button>
  )
}

export default BillingPortalButton
