import { getPayload } from 'payload'
import FeatureDAO from '@/app/(app)/_backend/common/dao/FeaturesDAO'
import User<PERSON>O from '@/app/(app)/_backend/common/dao/UserDAO'
import { errorHandler } from '@/app/(app)/_backend/common/exception/errorHandler'
import FeatureScopeService from '@/app/(app)/_backend/common/service/FeatureScopeService'
import config from '@payload-config'

export interface UserFeaturesResponse {
  features: string[]
}

export async function POST(request: Request) {
  try {
    const userHeader = request.headers.get('x-user')
    const user = JSON.parse(userHeader!)
    const payload = await getPayload({ config })
    const userDAO = new UserDAO(payload)
    const featuresDAO = new FeatureDAO(payload)
    const featureScopeService = new FeatureScopeService(userDAO, featuresDAO)
    const userFeatures = await featureScopeService.retrieveUserFeatures(user.id)

    return new Response(
      JSON.stringify({
        message: 'Features retrieved',
        data: {
          features: userFeatures,
        },
      }),
    )
  } catch (error) {
    return errorHandler(error)
  }
}
