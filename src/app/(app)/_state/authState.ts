import { create } from 'zustand'
import { createJSONStorage, persist } from 'zustand/middleware'
import { verifyUser } from '../_localApi/users'

export interface ClientAuthState {
  id: string
  firstName: string
  lastName: string
  email: string
  role: string
  freeCredits: number
  paidCredits: number
  rolloverCredits: number
}

interface AuthStore {
  user: ClientAuthState | null
  setUser: (user: ClientAuthState | null) => void
  hasEverLoggedIn: boolean
  setHasEverLoggedIn: (hasEverLoggedIn: boolean) => void
  isLoggedIn: boolean
  setIsLoggedIn: (isLoggedIn: boolean) => void
  expiry: number | null
  setExpiry: (expiry: number | null | undefined) => void
  rehydrated: boolean
  setRehydrated: (rehydrated: boolean) => void
  isLoading: boolean
  error: string | null
  setIsLoading: (isLoading: boolean) => void
  setError: (error: any) => void
  loadUser: () => Promise<void>
}

// Cookie storage implementation for Zustand
function createCookieStorage(key: string) {
  return {
    getItem: (name: string) => {
      const match = document.cookie.match(
        new RegExp('(^| )' + encodeURIComponent(name) + '=([^;]+)'),
      )
      return match ? decodeURIComponent(match[2]) : null
    },
    setItem: (name: string, value: string) => {
      try {
        const parsed = JSON.parse(value)
        const expiry = typeof parsed?.expiry === 'number' ? parsed.expiry : null

        let cookie = `${encodeURIComponent(name)}=${encodeURIComponent(value)}; path=/; SameSite=Lax; ${process.env.NODE_ENV === 'production' ? 'Secure' : ''}`
        if (expiry) {
          const expires = new Date(expiry).toUTCString()
          cookie += `; expires=${expires}`
        }

        document.cookie = cookie
      } catch (err) {
        document.cookie = `${encodeURIComponent(name)}=${encodeURIComponent(value)}; path=/; SameSite=Lax; ${process.env.NODE_ENV === 'production' ? 'Secure' : ''}`
      }
    },
    removeItem: (name: string) => {
      document.cookie = `${encodeURIComponent(name)}=; Max-Age=0; path=/`
    },
  }
}

const storage = typeof window !== 'undefined' ? createCookieStorage('auth-storage') : undefined

export const useAuthState = create<AuthStore>()(
  persist(
    (set) => ({
      user: null,
      setUser: (user: ClientAuthState | null) => set({ user }),
      isLoggedIn: false,
      setIsLoggedIn: (isLoggedIn: boolean) => set({ isLoggedIn }),
      expiry: null,
      setExpiry: (expiry: number | null | undefined) => set({ expiry }),
      rehydrated: false,
      setRehydrated: (rehydrated: boolean) => set({ rehydrated }),
      isLoading: false,
      setIsLoading: (isLoading: boolean) => set({ isLoading }),
      error: null,
      setError: (error: string | null) => set({ error }),
      hasEverLoggedIn: false,
      setHasEverLoggedIn: (hasEverLoggedIn: boolean) => set({ hasEverLoggedIn }),
      loadUser: async () => {
        set({ isLoading: true, error: null })
        try {
          const response = await verifyUser()
          const verifiedUser = response.data
          if (response.success) {
            set({
              user: {
                id: verifiedUser.id,
                firstName: verifiedUser.firstName,
                lastName: verifiedUser.lastName,
                email: verifiedUser.email,
                role: verifiedUser.role,
                freeCredits: verifiedUser.freeCredits,
                paidCredits: verifiedUser.paidCredits,
                rolloverCredits: verifiedUser.rolloverCredits,
              },
              expiry: verifiedUser.exp,
              isLoggedIn: true,
              isLoading: false,
              error: null,
            })
          }
        } catch (error: any) {
          set({
            user: null,
            isLoggedIn: false,
            expiry: null,
            isLoading: false,
            error: error?.message || 'Failed to load user',
          })
        } finally {
          set({ isLoading: false })
        }
      },
    }),
    {
      name: 'auth-storage',
      storage: storage ? createJSONStorage(() => storage) : undefined!,
      merge: (persistedState, currentState) => {
        const now = Date.now()
        const safePersisted = persistedState ?? {}
        const expiry = (safePersisted as AuthStore)?.expiry
        const isExpired = typeof expiry === 'number' && now > expiry * 1000
        if (isExpired) {
          return {
            ...currentState,
            user: null,
            isLoggedIn: false,
            expiry: null,
          }
        }

        return {
          ...currentState,
          ...safePersisted,
        }
      },
      onRehydrateStorage: () => (state) => {
        if (state) state.setRehydrated(true)
      },
    },
  ),
)
