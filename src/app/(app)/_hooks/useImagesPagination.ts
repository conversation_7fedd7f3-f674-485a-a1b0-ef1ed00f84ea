import { useQuery } from '@tanstack/react-query'
import { useEffect, useState } from 'react'
import { GeneratedImage } from '@/payload-types'
import { GeneratedImageStatus } from '@/types/GeneratedImageStatus'
import { fetchUserImages } from '../_localApi/imageGeneration'
import { ImageMetadata } from '../_types/ImageMetadata'

interface UseImagesPaginationOptions {
  page?: number
  pageSize?: number
  enabled?: boolean
  refetchInterval?: number | false
}

interface UseImagesPaginationResult {
  images: ImageMetadata[]
  pagination: {
    totalCount: number
    currentPage: number
    totalPages: number
    pageSize: number
    hasNextPage: boolean
    hasPrevPage: boolean
  }
  pendingImageGeneration: boolean
  paginationIsLoading: boolean
  error: Error | null
  refetch: () => void
}

export function useImagesPagination(
  options: UseImagesPaginationOptions = {},
): UseImagesPaginationResult {
  const { page = 1, pageSize = 5, enabled = true, refetchInterval = false } = options
  const [paginationCache, setPaginationCache] = useState({
    totalCount: 0,
    currentPage: page,
    totalPages: 0,
    pageSize,
    hasNextPage: false,
    hasPrevPage: false,
  })

  const { data, error, refetch, isLoading } = useQuery({
    queryKey: ['images-pagination', page, pageSize],
    queryFn: async () => {
      const response = await fetchUserImages(page, pageSize)
      const generatedImages: GeneratedImage[] = response.data.images
      const pagination = response.data.pagination

      // Transform GeneratedImage[] to ImageMetadata[]
      const imagePromises = generatedImages.map((image: GeneratedImage) => {
        return {
          id: image.id,
          src: image.url,
          width: image.width as number,
          height: image.height as number,
          prompt: image.prompt as string,
          actionType: image.taskType as string,
          status: image.status as GeneratedImageStatus,
        }
      })

      const imagesMetadata = await Promise.all(imagePromises)
      const completedImages = imagesMetadata.filter(
        (image) => image !== null && image.status === GeneratedImageStatus.COMPLETED,
      ) as ImageMetadata[]
      const pendingImages = imagesMetadata.filter(
        (image) => image !== null && image.status === GeneratedImageStatus.GENERATING,
      )
      const pendingImageGeneration = pendingImages.length > 0

      return {
        images: completedImages,
        pagination: pagination,
        pendingImageGeneration,
      }
    },
    enabled,
    refetchInterval,
  })

  useEffect(() => {
    if (data?.pagination) {
      setPaginationCache(data.pagination)
    }
  }, [data?.pagination])

  return {
    images: data?.images || [],
    pagination: paginationCache,
    pendingImageGeneration: data?.pendingImageGeneration || false,
    paginationIsLoading: isLoading,
    error: error as Error | null,
    refetch,
  }
}
