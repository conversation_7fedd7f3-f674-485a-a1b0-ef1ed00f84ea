'use client'

import { useMutation, useQuery } from '@tanstack/react-query'
import { useQueryClient } from '@tanstack/react-query'
import Avatar from 'boring-avatars'
import { Coins, IdCard, LogOut, Settings, TriangleAlert } from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useEffect, useRef, useState } from 'react'
import { preconnect } from 'react-dom'
import { BeatLoader } from 'react-spinners'
import { toast } from 'sonner'
import { cn } from '@/lib/utils'
import { Spacing } from '@/utilities/local/css'
import Divider from '../../_component/Divider'
import { Progress } from '../../_component/Progress'
import Text from '../../_component/Text'
import FlexContainer, {
  AlignItems,
  FlexDirection,
  JustifyContent,
} from '../../_cssComp/FlexContainer'
import GridContainer from '../../_cssComp/GridContainer'
import { DESKTOP_QUERY, useMediaQuery } from '../../_hooks/MediaQuery'
import { fetchSelfUser, fetchSelfUserWithSubscriptionPlan, logoutUser } from '../../_localApi/users'
import { useAuthUser } from '../../_provider/AuthProvider'
import { useAuthState } from '../../_state/authState'
import { UserWithSubscriptionPlan } from '../../api/user/subscriptionPlan/route'

interface Props {
  allowHover?: boolean
}

function CostMenuIndicator(props: Props) {
  const { allowHover = true } = props
  const queryClient = useQueryClient()
  const router = useRouter()
  const { user, setUser } = useAuthUser()
  const isDesktop = useMediaQuery(DESKTOP_QUERY)
  const [userWithSubscription, setUserWithSubscription] = useState<UserWithSubscriptionPlan>()
  const HOST_URL = process.env.NEXT_PUBLIC_HOST ?? 'http://localhost:3000'

  const [isOpen, setIsOpen] = useState(false)
  const containerRef = useRef<HTMLDivElement | null>(null)

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const handleOpenCloseDropdown = () => {
    if (isDesktop) {
      setIsOpen((prev) => !prev)
    }
  }

  const logoutMutation = useMutation({
    mutationFn: () => {
      return logoutUser()
    },
    onSuccess: async () => {
      useAuthState.setState({
        user: null,
        isLoggedIn: false,
        expiry: null,
        hasEverLoggedIn: true,
      })
      queryClient.clear()
      router.push(HOST_URL)
    },
    onError: () => {
      toast.error('Logout failed', {
        description: 'Something went wrong trying to log you out. Please try again.',
        duration: 0,
        position: 'top-right',
        icon: <TriangleAlert />,
      })
    },
  })
  const userQuery = useQuery({
    queryKey: ['user'],
    queryFn: () => fetchSelfUserWithSubscriptionPlan(),
  })
  useEffect(() => {
    if (userQuery.data && userQuery.data.success) {
      setUserWithSubscription(userQuery.data.data)
      setUser({
        id: userQuery.data.data.user.id,
        firstName: userQuery.data.data.user.firstName,
        lastName: userQuery.data.data.user.lastName,
        email: userQuery.data.data.user.email,
        role: userQuery.data.data.user.role,
        freeCredits: userQuery.data.data.user.freeCredits,
        paidCredits: userQuery.data.data.user.paidCredits,
        rolloverCredits: userQuery.data.data.user.rolloverCredits,
      })
    }
  }, [userQuery.dataUpdatedAt])
  const handleLogout = async () => {
    await logoutMutation.mutateAsync()
  }
  return userWithSubscription && user ? (
    <div className="relative w-full" ref={containerRef}>
      <FlexContainer
        className={cn(
          'gap-4 w-full cursor-pointer p-2 rounded-md',
          `${allowHover ? 'hover:bg-accent' : ''}`,
          `${isOpen ? 'bg-accent' : ''}`,
        )}
        direction={FlexDirection.ROW}
        align={AlignItems.CENTER}
        onClick={handleOpenCloseDropdown}
      >
        <FlexContainer
          className="gap-2 flex-1"
          align={AlignItems.END}
          direction={FlexDirection.COL}
        >
          <FlexContainer
            className="gap-2"
            align={AlignItems.CENTER}
            justify={JustifyContent.END}
            direction={FlexDirection.ROW}
          >
            <Coins size="12px" />
            <Text size="sm">
              {user.paidCredits + user.freeCredits + user.rolloverCredits} /{' '}
              {userWithSubscription.subscriptionPlan.credits}
            </Text>
          </FlexContainer>
          <Progress
            value={user.paidCredits + user.freeCredits + user.rolloverCredits}
            max={userWithSubscription.subscriptionPlan.credits}
          />
        </FlexContainer>
        <Avatar size={40} name={user?.firstName ?? ''} variant="beam" />
      </FlexContainer>
      {isOpen && (
        <div
          className={cn(
            'absolute top-full mt-4 border-black border-2 bg-white shadow-lg rounded-lg transition-opacity z-50',
            // 'left-1/2 -translate-x-1/2', // center it
            'right-2',
            'max-w-[90vw] w-[300px]', // keep it responsive
            'overflow-hidden',
            Spacing.ContentPadding,
          )}
        >
          <GridContainer className="w-[250px] grid-cols-1">
            <FlexContainer direction={FlexDirection.COL} className="gap-2">
              <FlexContainer
                direction={FlexDirection.ROW}
                className="gap-2 px-2"
                align={AlignItems.CENTER}
              >
                <IdCard size={18} />
                <Text size="xl" variant="emphasis">
                  {userWithSubscription.subscriptionPlan.name} ✨
                </Text>
              </FlexContainer>
              <FlexContainer
                direction={FlexDirection.ROW}
                className="gap-2 px-2"
                align={AlignItems.CENTER}
              >
                <Coins color={'#8fffd8'} />
                <Text size="sm" variant="description">
                  Available Credits: {user.freeCredits}
                </Text>
              </FlexContainer>
              <FlexContainer
                direction={FlexDirection.ROW}
                className="gap-2 px-2"
                align={AlignItems.CENTER}
              >
                <Coins color={'#a2d2ff'} />
                <Text size="sm" variant="description">
                  Total Credits: {userWithSubscription.subscriptionPlan.credits}
                </Text>
              </FlexContainer>
            </FlexContainer>
            <Divider />
            <Link href={`${HOST_URL}/dashboard/settings`}>
              <FlexContainer
                direction={FlexDirection.COL}
                className="hover:border-black hover:bg-accent border-transparent cursor-pointer border-2 w-full gap-2 rounded-sm bg-white p-2"
              >
                <FlexContainer
                  direction={FlexDirection.ROW}
                  className="gap-2"
                  align={AlignItems.CENTER}
                >
                  <Settings />
                  <Text variant="emphasis" size="base">
                    Settings
                  </Text>
                </FlexContainer>
              </FlexContainer>
            </Link>
            <FlexContainer
              direction={FlexDirection.COL}
              className="hover:border-black hover:bg-accent border-transparent cursor-pointer border-2 w-full gap-2 rounded-sm bg-white p-2"
              onClick={handleLogout}
            >
              <FlexContainer
                direction={FlexDirection.ROW}
                className="gap-2"
                align={AlignItems.CENTER}
              >
                <LogOut />
                <Text variant="emphasis" size="base">
                  Logout
                </Text>
              </FlexContainer>
            </FlexContainer>
          </GridContainer>
        </div>
      )}
    </div>
  ) : (
    <div className="flex items-center justify-end w-full h-full">
      <BeatLoader />
    </div>
  )
}

export default CostMenuIndicator
