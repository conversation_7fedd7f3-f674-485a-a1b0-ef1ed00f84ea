import Avatar from 'boring-avatars'
import { BookOpenText } from 'lucide-react'
import { Metadata } from 'next'
import NextImage from 'next/image'
import Link from 'next/link'
import { notFound } from 'next/navigation'
import { getPayload } from 'payload'
import { cache } from 'react'
import { isCategories, isCategory } from '@/collections/Categories'
import { isMedia } from '@/collections/Media'
import { Layout } from '@/collections/Posts'
import { isUser } from '@/collections/Users'
import { Media } from '@/payload-types'
import SerializeComponent from '@/utilities/local/blog/ReactSerializer'
import { SerializedLexicalNode } from '@/utilities/local/blog/types'
import { formatTimestamp } from '@/utilities/local/date'
import { asyncErrorBoundary } from '@/utilities/local/pageErrorHandling/asyncErrorBoundary'
import { capitalizeAllLetters, capitalizeAndReplace } from '@/utilities/local/string'
import config from '@payload-config'

import PostDAO from '../../../_backend/common/dao/PostDAO'
import PostService from '../../../_backend/common/service/PostService'
import BlockRender from '../../../_component/BlockRender'
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbSeparator,
  BreadcrumbPage,
} from '../../../_component/Breadcrumbs'
import { Button } from '../../../_component/Button'
import Card from '../../../_component/Card/CardSolo'
import PillTag from '../../../_component/PillTag'
import Text from '../../../_component/Text'
import Title, { HeaderLevel } from '../../../_component/Title'
import BlogContainer from '../../../_cssComp/BlogContainer'
import Container from '../../../_cssComp/Container'
import FlexContainer, {
  AlignItems,
  FlexDirection,
  JustifyContent,
} from '../../../_cssComp/FlexContainer'
import RecommendedSection from '../../../_templates/Blog/RecommendedSection'

let postService: PostService | null = null

async function getPostService() {
  if (!postService) {
    const payload = await getPayload({ config })
    const postDAO = new PostDAO(payload)
    postService = new PostService(postDAO)
  }
  return postService
}

const getBlogPost = cache(async (category: string, slug: string) => {
  const service = await getPostService()
  const blogPost = await service.getPostByCategoryAndSlug(category, slug)
  if (!blogPost) {
    console.error('Blog post not found for slug:', slug)
    throw new Error('Blog post not found') // Prevent caching null
  }
  return blogPost
})

export async function generateMetadata({
  params,
}: {
  params: Promise<{ category: string; slug: string }>
}): Promise<Metadata> {
  const blogCategory = (await params).category
  const blogSlug = (await params).slug
  const blogPost = await getBlogPost(blogCategory, blogSlug)
  if (blogPost == null) {
    return {
      title: 'Not Found',
    }
  }
  const blogImage = blogPost.image as Media
  return {
    title: blogPost.meta?.title,
    description: blogPost.meta?.description,
    openGraph: {
      title: blogPost.meta?.title ?? 'Blog Post',
      description: blogPost.meta?.description ?? 'A blog post on ColorAria',
      images: blogPost.image ? [{ url: blogImage.url ?? '', alt: blogImage.alt }] : [],
      siteName: 'ColorAria',
      type: 'article',
      url: `${process.env.NEXT_PUBLIC_HOST}/blog/${blogCategory.toLowerCase()}/${blogPost.slug}`,
    },
    twitter: {
      card: 'summary_large_image',
      title: blogPost.meta?.title ?? 'Blog Post',
      description: blogPost.meta?.description ?? 'A blog post on ColorAria',
      images: blogPost.image ? [{ url: blogImage.url ?? '', alt: blogImage.alt }] : [],
      site: 'ColorAria',
    },
    alternates: {
      canonical: `${process.env.NEXT_PUBLIC_HOST}/blog/${blogCategory.toLowerCase()}/${blogPost.slug}`,
    },
  }
}

async function BlogPage({ params }: { params: Promise<{ category: string; slug: string }> }) {
  const categorySlug = (await params).category
  const blogPost = await getBlogPost(categorySlug, (await params).slug)

  if (blogPost == null || blogPost.id == null) {
    return notFound()
  }

  return (
    <div className={'bg-white h-full mx-auto pt-4 px-2'}>
      <div>
        <Breadcrumb className="w-[80%]">
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">Home</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/blog">Blog</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink
                href={`/blog/${isCategory(blogPost.category) ? blogPost.category.slug : categorySlug}`}
              >
                {isCategory(blogPost.category)
                  ? blogPost.category.name
                  : capitalizeAndReplace(categorySlug, '-')}
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink
                href={`/blog/${categorySlug.toLowerCase()}/${blogPost.slug}`}
                className="truncate"
              >
                {' '}
                {blogPost.title}{' '}
              </BreadcrumbLink>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      <BlogContainer className={'bg-white h-full w-full pb-8'}>
        <FlexContainer direction={FlexDirection.COL} className="gap-4 w-full mt-8">
          <FlexContainer
            align={AlignItems.CENTER}
            justify={JustifyContent.CENTER}
            className="w-full"
          >
            <PillTag
              text={isCategory(blogPost.category) ? blogPost.category.name : blogPost.category}
            />
          </FlexContainer>
          <FlexContainer
            direction={FlexDirection.COL}
            justify={JustifyContent.CENTER}
            align={AlignItems.CENTER}
            className="w-full gap-2"
          >
            <Title level={HeaderLevel.H1} className="text-center">
              {blogPost.title}
            </Title>
            <Text variant="description">{formatTimestamp(blogPost.createdAt)}</Text>
          </FlexContainer>
          <FlexContainer direction={FlexDirection.COL} className="gap-2">
            <FlexContainer
              className="w-full"
              direction={FlexDirection.ROW}
              justify={JustifyContent.BETWEEN}
              align={AlignItems.CENTER}
            >
              {isUser(blogPost.author) && (
                <FlexContainer
                  align={AlignItems.CENTER}
                  className="gap-2"
                  direction={FlexDirection.ROW}
                >
                  <Avatar name={blogPost.author.author_name} variant="beam" size={36} />
                  <Text variant="emphasis">{blogPost.author.author_name}</Text>
                </FlexContainer>
              )}
              <Text variant="description">✨ {blogPost.readingTime} min read</Text>
            </FlexContainer>
            {isMedia(blogPost.image) && (
              <NextImage
                src={blogPost.image.url!}
                alt={blogPost.image.alt}
                width={blogPost.image.width!}
                height={blogPost.image.height!}
                className="rounded-2xl"
              />
            )}
          </FlexContainer>
          {blogPost.summary && (
            <div className="w-full">
              <Link href={'#summary_section'} className="w-full">
                <Button className="w-full" variant="default">
                  <FlexContainer align={AlignItems.CENTER} className="gap-2">
                    <BookOpenText />
                    Summary
                  </FlexContainer>
                </Button>
              </Link>
            </div>
          )}

          <div className={`w-full mt-4`}>
            {blogPost.layout && blogPost.layout.length > 0 && (
              <BlockRender layout={blogPost.layout as Layout[]} className="" />
            )}

            {/* <Serialize nodes={blogPost.layout[0].richText.root.children as SerializedLexicalNode[]} /> */}
          </div>
        </FlexContainer>

        {blogPost.summary && (
          <section id="summary_section" className="w-full mt-4 mb-4 ">
            <FlexContainer className="w-full mb-4">
              <Card title="TLDR;" className="bg-accent6-lighter">
                {/* <Serialize
                  nodes={blogPost.summary.root.children as SerializedLexicalNode[]}
                  separation="mb-1"
                /> */}
                <SerializeComponent data={blogPost.summary} />
              </Card>
            </FlexContainer>
          </section>
        )}
        <FlexContainer className="w-full">
          <RecommendedSection
            className="mt-4 w-full"
            category={blogPost.category}
            tags={blogPost.tags}
            ignoreBlogId={blogPost.id}
          />
        </FlexContainer>
      </BlogContainer>
    </div>
  )
}

export default asyncErrorBoundary(BlogPage)
