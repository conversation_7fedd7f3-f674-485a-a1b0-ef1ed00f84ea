'use client'
import { zodResolver } from '@hookform/resolvers/zod'
import { QueryClient, useMutation, useQuery } from '@tanstack/react-query'
import { Coins, CreditCard, Diamond, IdCard, Save, Star, UserRound } from 'lucide-react'
import Link from 'next/link'
import { useEffect, useMemo, useRef, useState } from 'react'
import { useForm } from 'react-hook-form'
import { MoonLoader } from 'react-spinners'
import { toast } from 'sonner'
import { z } from 'zod'
import { IconSize, Margin } from '@/utilities/local/css'
import { calculateNextMonthlyCreditRenewal } from '@/utilities/local/date'
import { Button } from '../../_component/Button'
import BillingPortalButton from '../../_component/Button/variants/BillingPortalButton'
import Card from '../../_component/Card/CardApplication'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../../_component/Form'
import { Input } from '../../_component/Input'
import { Label } from '../../_component/Label'
import Text from '../../_component/Text'
import Title, { HeaderLevel } from '../../_component/Title'
import Container from '../../_cssComp/Container'
import FlexContainer, {
  AlignItems,
  FlexDirection,
  JustifyContent,
} from '../../_cssComp/FlexContainer'
import GridContainer, { ColSpan, GridItem } from '../../_cssComp/GridContainer'
import { fetchSelfUserWithSubscriptionPlan, updateUser } from '../../_localApi/users'
import { useAuthUser } from '../../_provider/AuthProvider'
import HighlightNumericCard from '../../_templates/NumericCard/HighlightNumericCard'

function SettingsProfileComponent() {
  const queryClient = new QueryClient()
  const { user, setUser } = useAuthUser()
  const [stripeCustomerId, setStripeCustomerId] = useState<string | null>(null)
  const [bLoadingSave, setBLoadingSave] = useState(false)
  const [billingDate, setBillingDate] = useState<string>()
  const [renewalDate, setRenewalDate] = useState<string>()

  const updateUserMut = useMutation({
    mutationFn: (data: { firstName: string; lastName: string }) => {
      return updateUser(data.firstName, data.lastName)
    },
    onMutate: () => {
      setBLoadingSave(true)
    },
    onSuccess: async (result) => {
      queryClient.invalidateQueries({ queryKey: ['user'] })
      setUser({
        id: result.data.id,
        firstName: result.data.firstName,
        lastName: result.data.lastName,
        email: result.data.email,
        role: result.data.role,
        freeCredits: result.data.freeCredits,
        paidCredits: result.data.paidCredits,
        rolloverCredits: result.data.rolloverCredits,
      })
      toast.success('Your profile settings have been saved.', {
        position: 'top-right',
      })
    },
    onError: () => {
      toast.error('Something went wrong. Please try again.', {
        position: 'top-right',
      })
    },
    onSettled: () => {
      setBLoadingSave(false)
    },
  })

  const userQuery = useQuery({
    queryKey: ['user', updateUserMut.data],
    queryFn: () => fetchSelfUserWithSubscriptionPlan(),
  })

  const onSubmit = (values: z.infer<typeof formSchema>) => {
    if (form.formState.isDirty) {
      updateUserMut.mutateAsync({
        firstName: values.firstName,
        lastName: values.lastName,
      })
    } else {
      toast.info('No changes were made.', {
        position: 'top-right',
      })
    }
  }

  useEffect(() => {
    if (userQuery.data && userQuery.data.success) {
      setUser({
        firstName: userQuery.data.data.user.firstName,
        lastName: userQuery.data.data.user.lastName,
        email: userQuery.data.data.user.email,
        role: userQuery.data.data.user.role,
        id: userQuery.data.data.user.id,
        freeCredits: userQuery.data.data.user.freeCredits,
        paidCredits: userQuery.data.data.user.paidCredits,
        rolloverCredits: userQuery.data.data.user.rolloverCredits,
      })
      const subscriptionRecord = userQuery.data.data.subscriptionRecord
      const subscriptionPlan = userQuery.data.data.subscriptionPlan
      if (subscriptionRecord.endDate != null) {
        const billingDateObj = new Date(subscriptionRecord.endDate)
        const localBillingDate = billingDateObj.toLocaleDateString(undefined, {
          month: '2-digit',
          day: '2-digit',
          year: 'numeric',
        })
        setBillingDate(localBillingDate)
        if (subscriptionPlan.subscription_duration === 'month') {
          setRenewalDate(localBillingDate)
        } else {
          const newRenewalDate = calculateNextMonthlyCreditRenewal(
            subscriptionRecord.startDate,
            subscriptionRecord.endDate,
          )
          setRenewalDate(newRenewalDate)
        }
      }
      setStripeCustomerId(subscriptionRecord.stripeCustomerId)
    }
  }, [userQuery.dataUpdatedAt])

  const formSchema = z.object({
    firstName: z.string().min(1, {
      message: 'Name is required',
    }),
    lastName: z.string().min(1, {
      message: 'Name is required',
    }),
  })

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: useMemo(() => {
      return {
        firstName: user?.firstName || '',
        lastName: user?.lastName || '',
      }
    }, [user]),
  })

  useEffect(() => {
    if (user) {
      form.reset(user)
    }
  }, [user, userQuery.dataUpdatedAt])

  const TitleBar = () => {
    return (
      <FlexContainer justify={JustifyContent.BETWEEN} align={AlignItems.CENTER} className="w-full">
        <FlexContainer direction={FlexDirection.ROW} align={AlignItems.CENTER} className="gap-2">
          <UserRound />
          <Title level={HeaderLevel.H3}>My Profile</Title>
        </FlexContainer>
        <Button
          variant="emphasis"
          round="round"
          onClick={form.handleSubmit(onSubmit)}
          disabled={!form.formState.isDirty}
        >
          {bLoadingSave ? (
            <MoonLoader size={IconSize.Small} color={'#FFFFFF'} />
          ) : (
            <FlexContainer className="gap-2" align={AlignItems.CENTER}>
              <Save size={IconSize.Small} /> Save
            </FlexContainer>
          )}
        </Button>
      </FlexContainer>
    )
  }
  const ManageSusbcriptionTitlebar = () => {
    return (
      <FlexContainer justify={JustifyContent.BETWEEN} align={AlignItems.CENTER} className="w-full">
        <FlexContainer direction={FlexDirection.ROW} align={AlignItems.CENTER} className="gap-2">
          <CreditCard />
          <Title level={HeaderLevel.H3}>Manage my Subscription</Title>
        </FlexContainer>
      </FlexContainer>
    )
  }
  return (
    <Container>
      <FlexContainer direction={FlexDirection.COL} className="w-full gap-8">
        <GridContainer
          className={`gap-4 mt-[${Margin.Medium}] w-full pr-2`}
          rows={12}
          autoResponsive={false}
        >
          <GridItem colSpan={ColSpan.SPAN_12}>
            <Title className="mt-4">Settings</Title>
          </GridItem>
          <GridItem colSpan="col-span-12 md:col-span-6">
            <HighlightNumericCard
              title="Available Credits"
              value={String(
                (userQuery.data?.data?.user.freeCredits || 0) +
                  (userQuery.data?.data?.user.paidCredits || 0),
              )}
              icon={<Coins size={IconSize.Medium} />}
              isLoading={userQuery.isLoading}
            >
              Renews on: {renewalDate || "It's free!"}
            </HighlightNumericCard>
          </GridItem>
          <GridItem colSpan="col-span-12 md:col-span-6">
            <HighlightNumericCard
              title="Current Membership"
              value={userQuery.data?.data?.subscriptionPlan.name || 'Free'}
              icon={<IdCard size={IconSize.Medium} />}
              isLoading={userQuery.isLoading}
            >
              Billing Date: {billingDate || "It's free!"}
            </HighlightNumericCard>
          </GridItem>
        </GridContainer>
        <Card title={<TitleBar />}>
          <FlexContainer direction={FlexDirection.COL} className="space-y-4 w-full">
            <FlexContainer direction={FlexDirection.COL}>
              <Label>Email:</Label>
              <b>{user?.email}</b>
            </FlexContainer>
            <Form {...form}>
              <FlexContainer direction={FlexDirection.COL} className="space-y-8 font-bold w-full">
                <GridContainer className="gap-4 w-full">
                  <GridItem colSpan="col-span-12 md:col-span-6">
                    <FormField
                      control={form.control}
                      name="firstName"
                      render={({ field }) => (
                        <FormItem className={'w-full'}>
                          <FormLabel>First Name:</FormLabel>
                          <FormControl>
                            <Input className={'w-full'} placeholder="" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </GridItem>
                  <GridItem colSpan="col-span-12 md:col-span-6">
                    <FormField
                      control={form.control}
                      name="lastName"
                      render={({ field }) => (
                        <FormItem className={'w-full'}>
                          <FormLabel>Last name:</FormLabel>
                          <FormControl>
                            <Input className={'w-full'} placeholder="" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </GridItem>
                </GridContainer>
                {/* <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email:</FormLabel>
                    <FormControl>
                      <Input className={'w-full'} placeholder="" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              /> */}
              </FlexContainer>
            </Form>
          </FlexContainer>
        </Card>
        <Card title={<ManageSusbcriptionTitlebar />}>
          {userQuery.isLoading ? (
            <MoonLoader size={IconSize.Small} color={'#000000'} />
          ) : (
            <>
              {stripeCustomerId ? (
                <FlexContainer direction={FlexDirection.COL} className="gap-4">
                  <Text>
                    Manage your subscription and billing details through our secure Stripe billing
                    portal.
                  </Text>
                  <BillingPortalButton />
                </FlexContainer>
              ) : (
                <FlexContainer direction={FlexDirection.COL} className="gap-4">
                  <Text>
                    {"You're currently on the"} <b>Free Plan!</b> <br />
                    <b>Upgrade now to enjoy more credits and features!</b>
                  </Text>
                  <Link href="/pricing">
                    <Button variant="emphasis" size="lg">
                      <Star />
                      Upgrade Now!
                    </Button>
                  </Link>
                </FlexContainer>
              )}
            </>
          )}
        </Card>
      </FlexContainer>
    </Container>
  )
}

export default SettingsProfileComponent
