interface ImageDimensions {
  w: number
  h: number
}

export function getImageDimensions(base64: string): Promise<ImageDimensions> {
  return new Promise(function (resolved, rejected) {
    var i = new Image()
    i.onload = function () {
      resolved({ w: i.naturalHeight, h: i.naturalWidth })
    }
    i.src = base64
  })
}

export async function sketchingImage(base64Image: string): Promise<string> {
  // Helper function: applies filters to an ImageBitmap and returns a filtered canvas
  const filter = (bmp: ImageBitmap, filters: string = ''): HTMLCanvasElement => {
    const canvas = Object.assign(document.createElement('canvas'), {
      width: bmp.width,
      height: bmp.height,
    }) as HTMLCanvasElement

    const ctx = canvas.getContext('2d')
    if (!ctx) throw new Error('Failed to get canvas 2D context')

    ctx.filter = filters
    ctx.drawImage(bmp, 0, 0)
    return canvas
  }

  // Helper function: merges two canvases to generate a sketch-like image
  const generateSketch = (bnw: HTMLCanvasElement, blur: HTMLCanvasElement): HTMLCanvasElement => {
    const canvas = document.createElement('canvas') as HTMLCanvasElement
    canvas.width = bnw.width
    canvas.height = bnw.height

    // Fix for Safari iOS devices
    ;(canvas as any).__skipFilterPatch = true

    const ctx = canvas.getContext('2d')
    if (!ctx) throw new Error('Failed to get canvas 2D context')

    ctx.drawImage(bnw, 0, 0, canvas.width, canvas.height)
    ctx.globalCompositeOperation = 'color-dodge' //'color-dodge'
    ctx.imageSmoothingEnabled = false
    ctx.drawImage(blur, 0, 0, canvas.width, canvas.height)
    return canvas
  }

  // Helper function: converts a canvas to a base64-encoded string
  const canvasToBase64 = (canvas: HTMLCanvasElement): string => {
    return canvas.toDataURL('image/png') // Default: PNG format
  }

  // Step 1: Convert base64 input to ImageBitmap
  const loadImageBitmap = async (base64: string): Promise<ImageBitmap> => {
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.crossOrigin = 'Anonymous' // Allow cross-origin base64 images
      img.src = base64

      img.onload = () => resolve(createImageBitmap(img))
      img.onerror = (err) => reject(new Error('Failed to load base64 image: ' + err))
    })
  }

  // Load the image from base64
  const bmp: ImageBitmap = await loadImageBitmap(base64Image)

  // Step 2: Generate black & white and blur canvases using the filter function
  const bnw: HTMLCanvasElement = filter(bmp, 'grayscale(1) contrast(1)')
  const blur: HTMLCanvasElement = filter(bmp, 'grayscale(1) invert(1) blur(5px)')

  // Step 3: Merge the black & white and blur canvases
  const sketchImg: HTMLCanvasElement = generateSketch(bnw, blur)

  // Step 4: Convert the final canvas to a base64 string
  const base64Result: string = canvasToBase64(sketchImg)

  // Return the base64 representation of the sketch image
  return base64Result
}

function gcd(a: number, b: number): number {
  return b === 0 ? a : gcd(b, a % b)
}

export function getMimeTypeFromBase64(base64: string): string {
  const matches = base64.match(/^data:(.*?);base64,/)
  return matches ? matches[1] : 'application/octet-stream'
}

export async function getMimeTypeFromSrc(src: string): Promise<string> {
  const base64 = await imageUrlToBase64(src)
  const matches = base64.match(/^data:(.*?);base64,/)
  return matches ? matches[1] : 'application/octet-stream'
}

export function calculateAspectRatio(width: number, height: number): string {
  const divisor = gcd(width, height)
  const aspectWidth = width / divisor
  const aspectHeight = height / divisor
  return `${aspectWidth} : ${aspectHeight}`
}

export async function downloadCloudflareR2Image(imageUrl: string, fileName: string) {
  const response = await fetch(imageUrl)
  if (!response.ok) {
    console.error('Download went wrong!')
    throw new Error('Download went wrong!')
  }
  const blob = await response.blob()
  const blobUrl = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = blobUrl
  link.download = fileName
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

export function imageElementToBase64(img: HTMLImageElement): string {
  const canvas = document.createElement('canvas')
  canvas.width = img.naturalWidth
  canvas.height = img.naturalHeight

  const ctx = canvas.getContext('2d')
  if (!ctx) throw new Error('Could not get canvas context')

  ctx.drawImage(img, 0, 0)

  // Convert to Base64
  return canvas.toDataURL('image/png') // or 'image/jpeg'
}

export async function imageUrlToBase64(url: string): Promise<string> {
  const response = await fetch(url)
  const blob = await response.blob()

  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onloadend = () => {
      if (typeof reader.result === 'string') {
        resolve(reader.result)
      } else {
        reject('Failed to convert image to Base64.')
      }
    }
    reader.onerror = reject
    reader.readAsDataURL(blob)
  })
}

export const b64toBlob = (b64Data: string, contentType = '', sliceSize = 512) => {
  const byteCharacters = atob(b64Data)
  const byteArrays = []

  for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
    const slice = byteCharacters.slice(offset, offset + sliceSize)

    const byteNumbers = new Array(slice.length)
    for (let i = 0; i < slice.length; i++) {
      byteNumbers[i] = slice.charCodeAt(i)
    }

    const byteArray = new Uint8Array(byteNumbers)
    byteArrays.push(byteArray)
  }

  const blob = new Blob(byteArrays, { type: contentType })
  return blob
}

export const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => resolve(reader.result as string)
    reader.onerror = reject
    reader.readAsDataURL(file)
  })
}

export const isBase64Str = (src: string) => {
  return /^data:image\/[a-z]+;base64,/.test(src)
}
