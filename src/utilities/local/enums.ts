export enum ActionType {
  TEXT_TO_COLOR = 'Text to Coloring Page',
  SCRIBBLE_TO_COLOR = 'Scribble to Coloring Page',
  IMAGE_TO_COLOR = 'Image to Coloring Page',
  DRAWING_TO_SKETCH = 'Drawing to Sketch Art',
  AI_BACKGROUND_REMOVER = 'AI Background Remover',
  AI_UPSCALER = 'AI Upscaler',
  CONTRAST = 'Contrast',
}

//This should be exactly the same as backend
export enum FeaturesType {
  TEXT_TO_COLOR = 'text_to_color',
  TEXT_TO_COLOR_IMAGE_REPETITION = 'text_to_color_image_repetition',
  SCRIBBLE_TO_COLOR = 'scribble_to_color',
  IMAGE_TO_COLOR = 'image_to_color',
  IMAGE_TO_COLOR_IMAGE_REPETITION = 'image_to_color_image_repetition',
  AI_BACKGROUND_REMOVER = 'ai_background_remover',
  TRADITIONAL_UPSCALER = 'traditional_upscaler',
  HIGH_NATIVE_QUALITY = 'high_native_quality',
  IMAGE_LIBRARY = 'image_library',
}

// DB
// INSERT INTO "features" ("feature", "credit_cost")
// VALUES ('text_to_color', 0),
//        ('text_to_color_image_repetition', 1),
//        ('scribble_to_color', 1),
//        ('image_to_color', 0),
//        ('image_to_color_image_repetition', 2),
//        ('ai_background_remover', 1),
//        ('traditional_upscaler', 1),
//        ('high_native_quality', 2),
//        ('image_library', 0);
